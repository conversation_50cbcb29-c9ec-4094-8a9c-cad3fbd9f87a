using System.Buffers;
using System.Collections.Concurrent;
using System.IO.Pipelines;
using System.Net;
using System.Net.Sockets;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core.Protocol;
using MqttBroker.Network.Abstractions;
using MqttBroker.Network.Configuration;

namespace MqttBroker.Network.Connection;

/// <summary>
/// TCP 客户端连接实现
/// </summary>
public class TcpClientConnection : IClientConnection
{
    private readonly TcpClient _tcpClient;
    private readonly NetworkStream _networkStream;
    private readonly ILogger<TcpClientConnection> _logger;
    private readonly NetworkConfiguration _configuration;
    private readonly IMqttPacketParser _packetParser;
    private readonly IMqttPacketSerializer _packetSerializer;
    private readonly INetworkMiddlewareManager? _middlewareManager;
    
    private readonly PipeReader _reader;
    private readonly PipeWriter _writer;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private readonly object _lockObject = new();
    private readonly ClientConnectionStatistics _statistics = new();
    
    private Task? _readTask;
    private Task? _writeTask;
    private bool _disposed;

    /// <summary>
    /// 初始化 TCP 客户端连接
    /// </summary>
    /// <param name="tcpClient">TCP 客户端</param>
    /// <param name="configuration">网络配置</param>
    /// <param name="packetParser">数据包解析器</param>
    /// <param name="packetSerializer">数据包序列化器</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="middlewareManager">中间件管理器</param>
    public TcpClientConnection(
        TcpClient tcpClient,
        IOptions<NetworkConfiguration> configuration,
        IMqttPacketParser packetParser,
        IMqttPacketSerializer packetSerializer,
        ILogger<TcpClientConnection> logger,
        INetworkMiddlewareManager? middlewareManager = null)
    {
        _tcpClient = tcpClient ?? throw new ArgumentNullException(nameof(tcpClient));
        _configuration = configuration?.Value ?? throw new ArgumentNullException(nameof(configuration));
        _packetParser = packetParser ?? throw new ArgumentNullException(nameof(packetParser));
        _packetSerializer = packetSerializer ?? throw new ArgumentNullException(nameof(packetSerializer));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _middlewareManager = middlewareManager;

        _networkStream = _tcpClient.GetStream();
        _cancellationTokenSource = new CancellationTokenSource();

        // 配置管道
        var pipeOptions = new PipeOptions(
            pauseWriterThreshold: _configuration.Performance.BackpressureThreshold,
            resumeWriterThreshold: _configuration.Performance.BackpressureThreshold / 2,
            readerScheduler: PipeScheduler.ThreadPool,
            writerScheduler: PipeScheduler.ThreadPool,
            useSynchronizationContext: false);

        var pipe = new Pipe(pipeOptions);
        _reader = pipe.Reader;
        _writer = pipe.Writer;

        // 初始化属性
        Id = Guid.NewGuid().ToString("N");
        RemoteEndPoint = _tcpClient.Client.RemoteEndPoint ?? new IPEndPoint(IPAddress.None, 0);
        LocalEndPoint = _tcpClient.Client.LocalEndPoint ?? new IPEndPoint(IPAddress.None, 0);
        State = ConnectionState.Connecting;
        ConnectedAt = DateTime.UtcNow;
        LastActivity = DateTime.UtcNow;
        Properties = new ConcurrentDictionary<string, object>();

        // 初始化统计信息
        _statistics.ConnectionId = Id;
        _statistics.RemoteEndPoint = RemoteEndPoint.ToString() ?? "Unknown";
        _statistics.ConnectedAt = ConnectedAt;
        _statistics.LastActivity = LastActivity;

        _logger.LogDebug("TCP client connection created: {ConnectionId} from {RemoteEndPoint}", Id, RemoteEndPoint);
    }

    /// <summary>
    /// 连接唯一标识符
    /// </summary>
    public string Id { get; }

    /// <summary>
    /// 客户端 ID（MQTT 协议层面）
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// 远程端点
    /// </summary>
    public EndPoint RemoteEndPoint { get; }

    /// <summary>
    /// 本地端点
    /// </summary>
    public EndPoint LocalEndPoint { get; }

    /// <summary>
    /// 连接状态
    /// </summary>
    public ConnectionState State { get; private set; }

    /// <summary>
    /// 连接建立时间
    /// </summary>
    public DateTime ConnectedAt { get; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivity { get; private set; }

    /// <summary>
    /// MQTT 协议版本
    /// </summary>
    public MqttProtocolVersion? ProtocolVersion { get; set; }

    /// <summary>
    /// 是否已认证
    /// </summary>
    public bool IsAuthenticated { get; set; }

    /// <summary>
    /// 保活间隔（秒）
    /// </summary>
    public int KeepAliveInterval { get; set; }

    /// <summary>
    /// 连接属性（用于存储自定义数据）
    /// </summary>
    public IDictionary<string, object> Properties { get; }

    /// <summary>
    /// 数据包接收事件
    /// </summary>
    public event EventHandler<PacketReceivedEventArgs>? PacketReceived;

    /// <summary>
    /// 连接关闭事件
    /// </summary>
    public event EventHandler<ConnectionClosedEventArgs>? ConnectionClosed;

    /// <summary>
    /// 启动连接处理
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (State != ConnectionState.Connecting)
        {
            throw new InvalidOperationException($"Connection is not in connecting state: {State}");
        }

        try
        {
            State = ConnectionState.Connected;
            UpdateLastActivity();

            // 启动读写任务
            _readTask = ReadFromNetworkAsync(_cancellationTokenSource.Token);
            _writeTask = WriteToNetworkAsync(_cancellationTokenSource.Token);

            // 执行连接建立中间件
            if (_middlewareManager != null)
            {
                await _middlewareManager.ExecuteConnectionEstablishedAsync(this, cancellationToken);
            }

            _logger.LogDebug("TCP client connection started: {ConnectionId}", Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start TCP client connection: {ConnectionId}", Id);
            State = ConnectionState.Error;
            throw;
        }
    }

    /// <summary>
    /// 发送数据包
    /// </summary>
    /// <param name="packet">要发送的数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task SendPacketAsync(IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        if (packet == null)
            throw new ArgumentNullException(nameof(packet));

        if (State != ConnectionState.Connected)
        {
            throw new InvalidOperationException($"Connection is not connected: {State}");
        }

        try
        {
            // 执行数据包发送中间件
            if (_middlewareManager != null)
            {
                await _middlewareManager.ExecutePacketSendingAsync(packet, this, cancellationToken);
            }

            // 序列化数据包
            var protocolVersion = ProtocolVersion ?? MqttProtocolVersion.Version311;
            var data = _packetSerializer.SerializePacket(packet, protocolVersion);

            // 发送数据
            await SendDataAsync(data, cancellationToken);

            _statistics.PacketsSent++;
            UpdateLastActivity();

            _logger.LogTrace("Sent packet: Type={PacketType}, Size={Size} bytes, ConnectionId={ConnectionId}", 
                packet.PacketType, data.Length, Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send packet: Type={PacketType}, ConnectionId={ConnectionId}", 
                packet.PacketType, Id);
            _statistics.Errors++;
            throw;
        }
    }

    /// <summary>
    /// 发送原始数据
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task SendDataAsync(ReadOnlyMemory<byte> data, CancellationToken cancellationToken = default)
    {
        if (State != ConnectionState.Connected)
        {
            throw new InvalidOperationException($"Connection is not connected: {State}");
        }

        try
        {
            await _writer.WriteAsync(data, cancellationToken);
            _statistics.BytesSent += data.Length;
            UpdateLastActivity();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send data: Size={Size} bytes, ConnectionId={ConnectionId}", 
                data.Length, Id);
            _statistics.Errors++;
            throw;
        }
    }

    /// <summary>
    /// 关闭连接
    /// </summary>
    /// <param name="reason">关闭原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>关闭任务</returns>
    public async Task CloseAsync(DisconnectionReason reason = DisconnectionReason.ServerDisconnected, CancellationToken cancellationToken = default)
    {
        if (State == ConnectionState.Disconnected || State == ConnectionState.Disconnecting)
        {
            return;
        }

        State = ConnectionState.Disconnecting;

        try
        {
            // 执行连接关闭中间件
            if (_middlewareManager != null)
            {
                await _middlewareManager.ExecuteConnectionClosedAsync(this, reason, cancellationToken);
            }

            // 取消所有操作
            _cancellationTokenSource.Cancel();

            // 等待读写任务完成
            if (_readTask != null)
            {
                await _readTask.ConfigureAwait(false);
            }

            if (_writeTask != null)
            {
                await _writeTask.ConfigureAwait(false);
            }

            // 关闭网络流
            _networkStream.Close();
            _tcpClient.Close();

            State = ConnectionState.Disconnected;

            // 触发连接关闭事件
            ConnectionClosed?.Invoke(this, new ConnectionClosedEventArgs(reason));

            _logger.LogDebug("TCP client connection closed: {ConnectionId}, Reason={Reason}", Id, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error closing TCP client connection: {ConnectionId}", Id);
            State = ConnectionState.Error;
        }
    }

    /// <summary>
    /// 更新最后活动时间
    /// </summary>
    public void UpdateLastActivity()
    {
        LastActivity = DateTime.UtcNow;
        _statistics.LastActivity = LastActivity;
    }

    /// <summary>
    /// 获取连接统计信息
    /// </summary>
    /// <returns>连接统计信息</returns>
    public ClientConnectionStatistics GetStatistics()
    {
        _statistics.ClientId = ClientId;
        _statistics.ProtocolVersion = ProtocolVersion;
        _statistics.IsAuthenticated = IsAuthenticated;
        _statistics.KeepAliveInterval = KeepAliveInterval;

        return _statistics;
    }

    /// <summary>
    /// 从网络读取数据的异步任务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>读取任务</returns>
    private async Task ReadFromNetworkAsync(CancellationToken cancellationToken)
    {
        try
        {
            var buffer = new byte[_configuration.Connection.ReceiveBufferSize];

            while (!cancellationToken.IsCancellationRequested && State == ConnectionState.Connected)
            {
                try
                {
                    var bytesRead = await _networkStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);

                    if (bytesRead == 0)
                    {
                        // 连接已关闭
                        _logger.LogDebug("Network stream closed by remote peer: {ConnectionId}", Id);
                        await CloseAsync(DisconnectionReason.ClientDisconnected, cancellationToken);
                        break;
                    }

                    // 写入管道
                    var memory = _writer.GetMemory(bytesRead);
                    buffer.AsMemory(0, bytesRead).CopyTo(memory);
                    _writer.Advance(bytesRead);

                    var flushResult = await _writer.FlushAsync(cancellationToken);
                    if (flushResult.IsCompleted)
                    {
                        break;
                    }

                    _statistics.BytesReceived += bytesRead;
                    UpdateLastActivity();
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error reading from network stream: {ConnectionId}", Id);
                    _statistics.Errors++;
                    await CloseAsync(DisconnectionReason.NetworkError, cancellationToken);
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in network read task: {ConnectionId}", Id);
        }
        finally
        {
            await _writer.CompleteAsync();
        }
    }

    /// <summary>
    /// 向网络写入数据的异步任务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>写入任务</returns>
    private async Task WriteToNetworkAsync(CancellationToken cancellationToken)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested && State == ConnectionState.Connected)
            {
                try
                {
                    var result = await _reader.ReadAsync(cancellationToken);
                    var buffer = result.Buffer;

                    if (buffer.IsEmpty && result.IsCompleted)
                    {
                        break;
                    }

                    // 处理接收到的数据包
                    await ProcessReceivedDataAsync(buffer, cancellationToken);

                    _reader.AdvanceTo(buffer.End);

                    if (result.IsCompleted)
                    {
                        break;
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error writing to network stream: {ConnectionId}", Id);
                    _statistics.Errors++;
                    await CloseAsync(DisconnectionReason.NetworkError, cancellationToken);
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in network write task: {ConnectionId}", Id);
        }
        finally
        {
            await _reader.CompleteAsync();
        }
    }

    /// <summary>
    /// 处理接收到的数据
    /// </summary>
    /// <param name="buffer">接收到的数据缓冲区</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    private async Task ProcessReceivedDataAsync(ReadOnlySequence<byte> buffer, CancellationToken cancellationToken)
    {
        var sequence = buffer;

        while (!sequence.IsEmpty)
        {
            try
            {
                // 尝试解析数据包
                if (!_packetParser.TryParsePacketHeader(sequence, out var packetLength, out var headerLength))
                {
                    // 没有完整的数据包头，等待更多数据
                    break;
                }

                if (sequence.Length < packetLength)
                {
                    // 没有完整的数据包，等待更多数据
                    break;
                }

                // 提取完整的数据包
                var packetSequence = sequence.Slice(0, packetLength);
                var protocolVersion = ProtocolVersion ?? MqttProtocolVersion.Version311;
                var packet = _packetParser.ParsePacket(packetSequence, protocolVersion);

                if (packet != null)
                {
                    _statistics.PacketsReceived++;
                    UpdateLastActivity();

                    // 执行数据包接收中间件
                    if (_middlewareManager != null)
                    {
                        await _middlewareManager.ExecutePacketReceivedAsync(packet, this, cancellationToken);
                    }

                    // 触发数据包接收事件
                    PacketReceived?.Invoke(this, new PacketReceivedEventArgs(packet));

                    _logger.LogTrace("Received packet: Type={PacketType}, Size={Size} bytes, ConnectionId={ConnectionId}",
                        packet.PacketType, packetLength, Id);
                }
                else
                {
                    _logger.LogWarning("Failed to parse packet: Size={Size} bytes, ConnectionId={ConnectionId}",
                        packetLength, Id);
                    _statistics.Errors++;
                }

                // 移动到下一个数据包
                sequence = sequence.Slice(packetLength);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing received data: ConnectionId={ConnectionId}", Id);
                _statistics.Errors++;
                await CloseAsync(DisconnectionReason.ProtocolError, cancellationToken);
                break;
            }
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            CloseAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during TCP client connection disposal: {ConnectionId}", Id);
        }

        _cancellationTokenSource.Dispose();
        _networkStream.Dispose();
        _tcpClient.Dispose();

        GC.SuppressFinalize(this);
    }
}
