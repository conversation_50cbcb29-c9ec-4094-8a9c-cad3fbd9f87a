using System.Buffers;
using System.Net.Sockets;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MqttBroker.Core;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Network;
using MqttBroker.Network.Abstractions;
using Xunit;
using Xunit.Abstractions;

namespace MqttBroker.Tests.Integration;

/// <summary>
/// 网络层集成测试
/// </summary>
public class NetworkIntegrationTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly IHost _host;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<NetworkIntegrationTests> _logger;

    /// <summary>
    /// 初始化网络集成测试
    /// </summary>
    /// <param name="output">测试输出</param>
    public NetworkIntegrationTests(ITestOutputHelper output)
    {
        _output = output;

        // 创建测试配置
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["MqttBroker:Network:Tcp:Enabled"] = "true",
                ["MqttBroker:Network:Tcp:Port"] = "18830", // 使用不同的端口避免冲突
                ["MqttBroker:Network:Tcp:Address"] = "127.0.0.1",
                ["MqttBroker:Network:Connection:MaxConnections"] = "100",
                ["MqttBroker:Network:Connection:ConnectionTimeout"] = "30",
                ["MqttBroker:Network:Connection:KeepAliveTimeout"] = "60"
            })
            .Build();

        // 创建测试主机
        _host = Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
            .ConfigureServices(services =>
            {
                // 注册核心服务
                services.AddMqttBrokerCore();
                
                // 注册网络服务
                services.AddMqttBrokerNetwork(configuration);

                // 添加测试日志
                services.AddLogging(builder =>
                {
                    builder.AddProvider(new XunitLoggerProvider(_output));
                    builder.SetMinimumLevel(LogLevel.Debug);
                });
            })
            .Build();

        _serviceProvider = _host.Services;
        _logger = _serviceProvider.GetRequiredService<ILogger<NetworkIntegrationTests>>();
    }

    /// <summary>
    /// 测试 TCP 服务器启动和停止
    /// </summary>
    [Fact]
    public async Task TcpServer_StartAndStop_ShouldWork()
    {
        // Arrange
        var networkServer = _serviceProvider.GetRequiredService<INetworkServer>();

        // Act & Assert
        Assert.False(networkServer.IsRunning);

        await networkServer.StartAsync();
        Assert.True(networkServer.IsRunning);
        Assert.NotNull(networkServer.LocalEndPoint);

        await networkServer.StopAsync();
        Assert.False(networkServer.IsRunning);
    }

    /// <summary>
    /// 测试客户端连接
    /// </summary>
    [Fact]
    public async Task TcpServer_ClientConnection_ShouldWork()
    {
        // Arrange
        var networkServer = _serviceProvider.GetRequiredService<INetworkServer>();
        var connectionManager = _serviceProvider.GetRequiredService<IConnectionManager>();
        
        var clientConnectedTcs = new TaskCompletionSource<IClientConnection>();
        var clientDisconnectedTcs = new TaskCompletionSource<IClientConnection>();

        networkServer.ClientConnected += (sender, e) => clientConnectedTcs.SetResult(e.Connection);
        networkServer.ClientDisconnected += (sender, e) => clientDisconnectedTcs.SetResult(e.Connection);

        // Act
        await networkServer.StartAsync();

        // 创建 TCP 客户端连接
        using var tcpClient = new TcpClient();
        await tcpClient.ConnectAsync("127.0.0.1", 18830);

        // 等待连接建立
        var connection = await clientConnectedTcs.Task.WaitAsync(TimeSpan.FromSeconds(5));

        // Assert
        Assert.NotNull(connection);
        Assert.Equal(ConnectionState.Connected, connection.State);
        Assert.Equal(1, connectionManager.ConnectionCount);

        // 关闭客户端连接
        tcpClient.Close();

        // 等待连接断开
        var disconnectedConnection = await clientDisconnectedTcs.Task.WaitAsync(TimeSpan.FromSeconds(5));
        Assert.NotNull(disconnectedConnection);
        Assert.Equal(connection.Id, disconnectedConnection.Id);

        await networkServer.StopAsync();
    }

    /// <summary>
    /// 测试 PING 数据包处理
    /// </summary>
    [Fact]
    public async Task TcpServer_PingPacket_ShouldRespondWithPingResponse()
    {
        // Arrange
        var networkServer = _serviceProvider.GetRequiredService<INetworkServer>();
        var packetSerializer = _serviceProvider.GetRequiredService<IMqttPacketSerializer>();
        var packetParser = _serviceProvider.GetRequiredService<IMqttPacketParser>();

        var clientConnectedTcs = new TaskCompletionSource<IClientConnection>();
        networkServer.ClientConnected += (sender, e) => clientConnectedTcs.SetResult(e.Connection);

        await networkServer.StartAsync();

        // Act
        using var tcpClient = new TcpClient();
        await tcpClient.ConnectAsync("127.0.0.1", 18830);
        var stream = tcpClient.GetStream();

        // 等待连接建立
        var connection = await clientConnectedTcs.Task.WaitAsync(TimeSpan.FromSeconds(5));

        // 创建并发送 PING 请求
        var pingRequest = MqttPingReqPacket.Create();
        var pingRequestData = packetSerializer.SerializePacket(pingRequest, MqttProtocolVersion.Version311);
        await stream.WriteAsync(pingRequestData);

        // 读取响应
        var responseBuffer = new byte[1024];
        var bytesRead = await stream.ReadAsync(responseBuffer, 0, responseBuffer.Length);

        // Assert
        Assert.True(bytesRead > 0);

        var responseSequence = new ReadOnlySequence<byte>(responseBuffer.AsMemory(0, bytesRead));
        var responsePacket = packetParser.ParsePacket(responseSequence, MqttProtocolVersion.Version311);

        Assert.NotNull(responsePacket);
        Assert.IsType<MqttPingRespPacket>(responsePacket);

        await networkServer.StopAsync();
    }

    /// <summary>
    /// 测试连接限制
    /// </summary>
    [Fact]
    public async Task TcpServer_ConnectionLimit_ShouldRejectExcessConnections()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["MqttBroker:Network:Tcp:Enabled"] = "true",
                ["MqttBroker:Network:Tcp:Port"] = "18831",
                ["MqttBroker:Network:Tcp:Address"] = "127.0.0.1",
                ["MqttBroker:Network:Connection:MaxConnections"] = "2" // 限制为 2 个连接
            })
            .Build();

        using var testHost = Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
            .ConfigureServices(services =>
            {
                services.AddMqttBrokerCore();
                services.AddMqttBrokerNetwork(configuration);
                services.AddLogging(builder => builder.AddProvider(new XunitLoggerProvider(_output)));
            })
            .Build();

        var networkServer = testHost.Services.GetRequiredService<INetworkServer>();
        var connectionManager = testHost.Services.GetRequiredService<IConnectionManager>();

        await networkServer.StartAsync();

        // Act
        var clients = new List<TcpClient>();
        try
        {
            // 创建 3 个连接（超过限制）
            for (int i = 0; i < 3; i++)
            {
                var client = new TcpClient();
                await client.ConnectAsync("127.0.0.1", 18831);
                clients.Add(client);
                
                // 给服务器一些时间处理连接
                await Task.Delay(100);
            }

            // 等待连接处理完成
            await Task.Delay(1000);

            // Assert
            Assert.True(connectionManager.ConnectionCount <= 2);
        }
        finally
        {
            // 清理
            foreach (var client in clients)
            {
                client.Close();
                client.Dispose();
            }
            
            await networkServer.StopAsync();
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _host?.Dispose();
    }
}

/// <summary>
/// Xunit 日志提供者
/// </summary>
public class XunitLoggerProvider : ILoggerProvider
{
    private readonly ITestOutputHelper _output;

    public XunitLoggerProvider(ITestOutputHelper output)
    {
        _output = output;
    }

    public ILogger CreateLogger(string categoryName)
    {
        return new XunitLogger(_output, categoryName);
    }

    public void Dispose() { }
}

/// <summary>
/// Xunit 日志记录器
/// </summary>
public class XunitLogger : ILogger
{
    private readonly ITestOutputHelper _output;
    private readonly string _categoryName;

    public XunitLogger(ITestOutputHelper output, string categoryName)
    {
        _output = output;
        _categoryName = categoryName;
    }

    public IDisposable? BeginScope<TState>(TState state) where TState : notnull => null;

    public bool IsEnabled(LogLevel logLevel) => true;

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        try
        {
            _output.WriteLine($"[{logLevel}] {_categoryName}: {formatter(state, exception)}");
            if (exception != null)
            {
                _output.WriteLine(exception.ToString());
            }
        }
        catch
        {
            // 忽略输出错误
        }
    }
}
