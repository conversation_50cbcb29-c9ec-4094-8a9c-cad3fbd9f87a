using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using MqttBroker.Network.Abstractions;
using MqttBroker.Network.Configuration;
using MqttBroker.Network.Connection;
using MqttBroker.Network.Middleware;
using MqttBroker.Network.PacketHandlers;
using MqttBroker.Network.Server;
using MqttBroker.Network.Services;

namespace MqttBroker.Network;

/// <summary>
/// MQTT Broker 网络服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 MQTT Broker 网络服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttBrokerNetwork(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册配置
        var networkSection = configuration.GetSection(NetworkConfiguration.SectionName);
        services.Configure<NetworkConfiguration>(networkSection);

        // 注册核心服务
        services.AddSingleton<IConnectionManager, ConnectionManager>();
        services.AddSingleton<IPacketHandlerManager, PacketHandlerManager>();
        services.AddSingleton<INetworkMiddlewareManager, NetworkMiddlewareManager>();

        // 注册网络服务器
        services.AddSingleton<INetworkServer, TcpNetworkServer>();

        // 注册后台服务
        services.AddHostedService<NetworkService>();

        // 注册默认中间件
        services.AddSingleton<INetworkMiddleware, ConnectionLoggingMiddleware>();

        // 注册默认数据包处理器
        services.AddSingleton<IPacketHandler, PingPacketHandler>();

        // 注册客户端连接（作为瞬态服务）
        services.AddTransient<TcpClientConnection>();

        return services;
    }

    /// <summary>
    /// 添加网络中间件
    /// </summary>
    /// <typeparam name="T">中间件类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddNetworkMiddleware<T>(this IServiceCollection services)
        where T : class, INetworkMiddleware
    {
        services.AddSingleton<INetworkMiddleware, T>();
        return services;
    }

    /// <summary>
    /// 添加数据包处理器
    /// </summary>
    /// <typeparam name="T">处理器类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddPacketHandler<T>(this IServiceCollection services)
        where T : class, IPacketHandler
    {
        services.AddSingleton<IPacketHandler, T>();
        return services;
    }
}
