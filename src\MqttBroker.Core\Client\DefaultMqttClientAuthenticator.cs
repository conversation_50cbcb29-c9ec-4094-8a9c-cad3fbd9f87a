using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MqttBroker.Core.Protocol.Packets;
using System.Text.RegularExpressions;

namespace MqttBroker.Core.Client;

/// <summary>
/// 默认 MQTT 客户端认证器
/// </summary>
public class DefaultMqttClientAuthenticator : IMqttClientAuthenticator
{
    private readonly ILogger<DefaultMqttClientAuthenticator> _logger;
    private readonly MqttClientAuthenticationOptions _options;
    private static readonly Regex ClientIdRegex = new(@"^[a-zA-Z0-9_-]{1,23}$", RegexOptions.Compiled);

    /// <summary>
    /// 初始化默认客户端认证器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="options">认证选项</param>
    public DefaultMqttClientAuthenticator(
        ILogger<DefaultMqttClientAuthenticator> logger,
        IOptions<MqttClientAuthenticationOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
    }

    /// <summary>
    /// 认证客户端
    /// </summary>
    /// <param name="connectPacket">CONNECT 数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证结果</returns>
    public async Task<MqttAuthenticationResult> AuthenticateAsync(MqttConnectPacket connectPacket, CancellationToken cancellationToken = default)
    {
        if (connectPacket == null)
            throw new ArgumentNullException(nameof(connectPacket));

        try
        {
            // 验证客户端 ID
            if (!IsClientIdValid(connectPacket.ClientId))
            {
                _logger.LogWarning("Invalid client ID: {ClientId}", connectPacket.ClientId);
                return MqttAuthenticationResult.InvalidClientId($"Invalid client ID: {connectPacket.ClientId}");
            }

            // 如果不需要认证，直接返回成功
            if (!_options.RequireAuthentication)
            {
                _logger.LogDebug("Authentication not required, allowing client: {ClientId}", connectPacket.ClientId);
                return MqttAuthenticationResult.Success(connectPacket.Username);
            }

            // 检查是否提供了用户名和密码
            if (string.IsNullOrEmpty(connectPacket.Username))
            {
                _logger.LogWarning("Username is required but not provided for client: {ClientId}", connectPacket.ClientId);
                return MqttAuthenticationResult.BadCredentials("Username is required");
            }

            if (string.IsNullOrEmpty(connectPacket.Password))
            {
                _logger.LogWarning("Password is required but not provided for client: {ClientId}", connectPacket.ClientId);
                return MqttAuthenticationResult.BadCredentials("Password is required");
            }

            // 执行用户名密码验证
            var authResult = await ValidateCredentialsAsync(connectPacket.Username, connectPacket.Password, cancellationToken);
            
            if (authResult.IsSuccess)
            {
                _logger.LogInformation("Client authenticated successfully: {ClientId}, Username: {Username}", 
                    connectPacket.ClientId, connectPacket.Username);
            }
            else
            {
                _logger.LogWarning("Authentication failed for client: {ClientId}, Username: {Username}, Reason: {Reason}", 
                    connectPacket.ClientId, connectPacket.Username, authResult.FailureReason);
            }

            return authResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during authentication for client: {ClientId}", connectPacket.ClientId);
            return MqttAuthenticationResult.ServerUnavailable("Authentication service error");
        }
    }

    /// <summary>
    /// 验证客户端 ID 是否有效
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <returns>如果有效则返回 true，否则返回 false</returns>
    public bool IsClientIdValid(string clientId)
    {
        if (string.IsNullOrEmpty(clientId))
        {
            return _options.AllowEmptyClientId;
        }

        // 检查长度限制
        if (clientId.Length > _options.MaxClientIdLength)
        {
            return false;
        }

        // 检查字符限制
        if (_options.UseStrictClientIdValidation)
        {
            return ClientIdRegex.IsMatch(clientId);
        }

        // 宽松验证：只检查不包含控制字符
        return !clientId.Any(c => char.IsControl(c));
    }

    /// <summary>
    /// 生成客户端 ID（当客户端未提供时）
    /// </summary>
    /// <returns>生成的客户端 ID</returns>
    public string GenerateClientId()
    {
        return $"auto_{Guid.NewGuid():N}"[..23]; // 限制在 23 个字符内
    }

    /// <summary>
    /// 验证用户凭据
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证结果</returns>
    protected virtual async Task<MqttAuthenticationResult> ValidateCredentialsAsync(string username, string password, CancellationToken cancellationToken)
    {
        // 简单的静态用户验证（生产环境应该使用数据库或外部认证服务）
        if (_options.StaticUsers.TryGetValue(username, out var expectedPassword))
        {
            if (password == expectedPassword)
            {
                var userClaims = new Dictionary<string, object>
                {
                    ["username"] = username,
                    ["authenticated_at"] = DateTime.UtcNow
                };

                return MqttAuthenticationResult.Success(username, userClaims);
            }
        }

        // 模拟异步操作
        await Task.Delay(100, cancellationToken);

        return MqttAuthenticationResult.BadCredentials($"Invalid credentials for user: {username}");
    }
}

/// <summary>
/// MQTT 客户端认证选项
/// </summary>
public class MqttClientAuthenticationOptions
{
    /// <summary>
    /// 是否需要认证
    /// </summary>
    public bool RequireAuthentication { get; set; } = true;

    /// <summary>
    /// 是否允许空客户端 ID
    /// </summary>
    public bool AllowEmptyClientId { get; set; } = false;

    /// <summary>
    /// 最大客户端 ID 长度
    /// </summary>
    public int MaxClientIdLength { get; set; } = 23;

    /// <summary>
    /// 是否使用严格的客户端 ID 验证
    /// </summary>
    public bool UseStrictClientIdValidation { get; set; } = true;

    /// <summary>
    /// 静态用户列表（用于简单认证）
    /// </summary>
    public Dictionary<string, string> StaticUsers { get; set; } = new()
    {
        ["admin"] = "admin123",
        ["test"] = "test123",
        ["guest"] = "guest123"
    };

    /// <summary>
    /// 认证超时时间（毫秒）
    /// </summary>
    public int AuthenticationTimeoutMs { get; set; } = 5000;
}
