using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Network;

namespace MqttBroker.Core.Client;

/// <summary>
/// MQTT 客户端接口
/// </summary>
public interface IMqttClient : IDisposable
{
    /// <summary>
    /// 客户端 ID
    /// </summary>
    string ClientId { get; }

    /// <summary>
    /// 网络连接
    /// </summary>
    IClientConnection Connection { get; }

    /// <summary>
    /// MQTT 协议版本
    /// </summary>
    MqttProtocolVersion ProtocolVersion { get; }

    /// <summary>
    /// 是否已认证
    /// </summary>
    bool IsAuthenticated { get; }

    /// <summary>
    /// 是否为 Clean Session
    /// </summary>
    bool CleanSession { get; }

    /// <summary>
    /// 保活间隔（秒）
    /// </summary>
    ushort KeepAliveInterval { get; }

    /// <summary>
    /// 连接建立时间
    /// </summary>
    DateTime ConnectedAt { get; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    DateTime LastActivity { get; }

    /// <summary>
    /// 用户名
    /// </summary>
    string? Username { get; }

    /// <summary>
    /// 遗嘱消息
    /// </summary>
    MqttWillMessage? WillMessage { get; }

    /// <summary>
    /// 客户端状态
    /// </summary>
    MqttClientState State { get; }

    /// <summary>
    /// 客户端属性（用于存储自定义数据）
    /// </summary>
    IDictionary<string, object> Properties { get; }

    /// <summary>
    /// 数据包接收事件
    /// </summary>
    event EventHandler<MqttPacketReceivedEventArgs>? PacketReceived;

    /// <summary>
    /// 客户端状态变更事件
    /// </summary>
    event EventHandler<MqttClientStateChangedEventArgs>? StateChanged;

    /// <summary>
    /// 发送 MQTT 数据包
    /// </summary>
    /// <param name="packet">要发送的数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task SendPacketAsync(IMqttPacket packet, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理接收到的 MQTT 数据包
    /// </summary>
    /// <param name="packet">接收到的数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task HandlePacketAsync(IMqttPacket packet, CancellationToken cancellationToken = default);

    /// <summary>
    /// 断开客户端连接
    /// </summary>
    /// <param name="reason">断开原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>断开任务</returns>
    Task DisconnectAsync(DisconnectionReason reason = DisconnectionReason.ServerDisconnected, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新最后活动时间
    /// </summary>
    void UpdateLastActivity();

    /// <summary>
    /// 检查客户端是否超时
    /// </summary>
    /// <returns>如果超时则返回 true，否则返回 false</returns>
    bool IsTimeout();

    /// <summary>
    /// 获取客户端统计信息
    /// </summary>
    /// <returns>客户端统计信息</returns>
    MqttClientStatistics GetStatistics();
}

/// <summary>
/// MQTT 客户端状态
/// </summary>
public enum MqttClientState
{
    /// <summary>
    /// 正在连接
    /// </summary>
    Connecting,

    /// <summary>
    /// 已连接
    /// </summary>
    Connected,

    /// <summary>
    /// 正在认证
    /// </summary>
    Authenticating,

    /// <summary>
    /// 已认证
    /// </summary>
    Authenticated,

    /// <summary>
    /// 正在断开
    /// </summary>
    Disconnecting,

    /// <summary>
    /// 已断开
    /// </summary>
    Disconnected,

    /// <summary>
    /// 错误状态
    /// </summary>
    Error
}

/// <summary>
/// MQTT 数据包接收事件参数
/// </summary>
public class MqttPacketReceivedEventArgs : EventArgs
{
    /// <summary>
    /// 接收到的数据包
    /// </summary>
    public IMqttPacket Packet { get; }

    /// <summary>
    /// 初始化数据包接收事件参数
    /// </summary>
    /// <param name="packet">接收到的数据包</param>
    public MqttPacketReceivedEventArgs(IMqttPacket packet)
    {
        Packet = packet ?? throw new ArgumentNullException(nameof(packet));
    }
}

/// <summary>
/// MQTT 客户端状态变更事件参数
/// </summary>
public class MqttClientStateChangedEventArgs : EventArgs
{
    /// <summary>
    /// 旧状态
    /// </summary>
    public MqttClientState OldState { get; }

    /// <summary>
    /// 新状态
    /// </summary>
    public MqttClientState NewState { get; }

    /// <summary>
    /// 初始化客户端状态变更事件参数
    /// </summary>
    /// <param name="oldState">旧状态</param>
    /// <param name="newState">新状态</param>
    public MqttClientStateChangedEventArgs(MqttClientState oldState, MqttClientState newState)
    {
        OldState = oldState;
        NewState = newState;
    }
}

/// <summary>
/// MQTT 客户端统计信息
/// </summary>
public class MqttClientStatistics
{
    /// <summary>
    /// 客户端 ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 连接 ID
    /// </summary>
    public string ConnectionId { get; set; } = string.Empty;

    /// <summary>
    /// 远程端点
    /// </summary>
    public string RemoteEndPoint { get; set; } = string.Empty;

    /// <summary>
    /// 连接建立时间
    /// </summary>
    public DateTime ConnectedAt { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivity { get; set; }

    /// <summary>
    /// 连接持续时间
    /// </summary>
    public TimeSpan Duration => DateTime.UtcNow - ConnectedAt;

    /// <summary>
    /// 接收的字节数
    /// </summary>
    public long BytesReceived { get; set; }

    /// <summary>
    /// 发送的字节数
    /// </summary>
    public long BytesSent { get; set; }

    /// <summary>
    /// 接收的数据包数
    /// </summary>
    public long PacketsReceived { get; set; }

    /// <summary>
    /// 发送的数据包数
    /// </summary>
    public long PacketsSent { get; set; }

    /// <summary>
    /// 错误数
    /// </summary>
    public long Errors { get; set; }

    /// <summary>
    /// MQTT 协议版本
    /// </summary>
    public MqttProtocolVersion ProtocolVersion { get; set; }

    /// <summary>
    /// 是否已认证
    /// </summary>
    public bool IsAuthenticated { get; set; }

    /// <summary>
    /// 是否为 Clean Session
    /// </summary>
    public bool CleanSession { get; set; }

    /// <summary>
    /// 保活间隔（秒）
    /// </summary>
    public ushort KeepAliveInterval { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// 客户端状态
    /// </summary>
    public MqttClientState State { get; set; }
}
