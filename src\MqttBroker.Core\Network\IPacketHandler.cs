using MqttBroker.Core.Protocol;

namespace MqttBroker.Core.Network;

/// <summary>
/// 数据包处理器接口
/// </summary>
public interface IPacketHandler
{
    /// <summary>
    /// 支持的数据包类型
    /// </summary>
    MqttPacketType PacketType { get; }

    /// <summary>
    /// 处理数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="packet">数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task HandleAsync(IClientConnection connection, IMqttPacket packet, CancellationToken cancellationToken = default);
}
