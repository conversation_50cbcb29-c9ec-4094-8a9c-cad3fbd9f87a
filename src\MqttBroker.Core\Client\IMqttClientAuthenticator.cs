using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Client;

/// <summary>
/// MQTT 客户端认证器接口
/// </summary>
public interface IMqttClientAuthenticator
{
    /// <summary>
    /// 认证客户端
    /// </summary>
    /// <param name="connectPacket">CONNECT 数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证结果</returns>
    Task<MqttAuthenticationResult> AuthenticateAsync(MqttConnectPacket connectPacket, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证客户端 ID 是否有效
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <returns>如果有效则返回 true，否则返回 false</returns>
    bool IsClientIdValid(string clientId);

    /// <summary>
    /// 生成客户端 ID（当客户端未提供时）
    /// </summary>
    /// <returns>生成的客户端 ID</returns>
    string GenerateClientId();
}

/// <summary>
/// MQTT 认证结果
/// </summary>
public class MqttAuthenticationResult
{
    /// <summary>
    /// 认证是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 失败原因
    /// </summary>
    public string? FailureReason { get; set; }

    /// <summary>
    /// 连接返回码 (MQTT 3.1.1)
    /// </summary>
    public MqttConnectReturnCode ReturnCode { get; set; }

    /// <summary>
    /// 原因码 (MQTT 5.0)
    /// </summary>
    public MqttReasonCode ReasonCode { get; set; }

    /// <summary>
    /// 认证的用户名
    /// </summary>
    public string? AuthenticatedUsername { get; set; }

    /// <summary>
    /// 用户角色或权限信息
    /// </summary>
    public Dictionary<string, object> UserClaims { get; set; } = new();

    /// <summary>
    /// 创建成功的认证结果
    /// </summary>
    /// <param name="username">认证的用户名</param>
    /// <param name="userClaims">用户声明</param>
    /// <returns>认证结果</returns>
    public static MqttAuthenticationResult Success(string? username = null, Dictionary<string, object>? userClaims = null)
    {
        return new MqttAuthenticationResult
        {
            IsSuccess = true,
            ReturnCode = MqttConnectReturnCode.ConnectionAccepted,
            ReasonCode = MqttReasonCode.Success,
            AuthenticatedUsername = username,
            UserClaims = userClaims ?? new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// 创建失败的认证结果
    /// </summary>
    /// <param name="returnCode">返回码</param>
    /// <param name="reasonCode">原因码</param>
    /// <param name="failureReason">失败原因</param>
    /// <returns>认证结果</returns>
    public static MqttAuthenticationResult Failure(MqttConnectReturnCode returnCode, MqttReasonCode reasonCode, string? failureReason = null)
    {
        return new MqttAuthenticationResult
        {
            IsSuccess = false,
            ReturnCode = returnCode,
            ReasonCode = reasonCode,
            FailureReason = failureReason
        };
    }

    /// <summary>
    /// 创建客户端 ID 无效的认证结果
    /// </summary>
    /// <param name="failureReason">失败原因</param>
    /// <returns>认证结果</returns>
    public static MqttAuthenticationResult InvalidClientId(string? failureReason = null)
    {
        return Failure(
            MqttConnectReturnCode.IdentifierRejected,
            MqttReasonCode.ClientIdentifierNotValid,
            failureReason ?? "Invalid client identifier"
        );
    }

    /// <summary>
    /// 创建认证失败的认证结果
    /// </summary>
    /// <param name="failureReason">失败原因</param>
    /// <returns>认证结果</returns>
    public static MqttAuthenticationResult BadCredentials(string? failureReason = null)
    {
        return Failure(
            MqttConnectReturnCode.BadUsernameOrPassword,
            MqttReasonCode.BadUserNameOrPassword,
            failureReason ?? "Bad username or password"
        );
    }

    /// <summary>
    /// 创建未授权的认证结果
    /// </summary>
    /// <param name="failureReason">失败原因</param>
    /// <returns>认证结果</returns>
    public static MqttAuthenticationResult NotAuthorized(string? failureReason = null)
    {
        return Failure(
            MqttConnectReturnCode.NotAuthorized,
            MqttReasonCode.NotAuthorized,
            failureReason ?? "Not authorized"
        );
    }

    /// <summary>
    /// 创建服务器不可用的认证结果
    /// </summary>
    /// <param name="failureReason">失败原因</param>
    /// <returns>认证结果</returns>
    public static MqttAuthenticationResult ServerUnavailable(string? failureReason = null)
    {
        return Failure(
            MqttConnectReturnCode.ServerUnavailable,
            MqttReasonCode.ServerUnavailable,
            failureReason ?? "Server unavailable"
        );
    }
}
