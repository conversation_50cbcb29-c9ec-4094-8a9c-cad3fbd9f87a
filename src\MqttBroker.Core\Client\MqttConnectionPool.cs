using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace MqttBroker.Core.Client;

/// <summary>
/// MQTT 连接池接口
/// </summary>
public interface IMqttConnectionPool : IDisposable
{
    /// <summary>
    /// 当前池中的连接数
    /// </summary>
    int PoolSize { get; }

    /// <summary>
    /// 最大池大小
    /// </summary>
    int MaxPoolSize { get; }

    /// <summary>
    /// 活跃连接数
    /// </summary>
    int ActiveConnections { get; }

    /// <summary>
    /// 从池中获取连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <returns>连接对象，如果池中没有可用连接则返回 null</returns>
    IMqttClient? GetConnection(string clientId);

    /// <summary>
    /// 将连接返回到池中
    /// </summary>
    /// <param name="client">要返回的客户端连接</param>
    /// <returns>如果成功返回到池中则返回 true，否则返回 false</returns>
    bool ReturnConnection(IMqttClient client);

    /// <summary>
    /// 添加连接到池中
    /// </summary>
    /// <param name="client">要添加的客户端连接</param>
    /// <returns>如果成功添加则返回 true，否则返回 false</returns>
    bool AddConnection(IMqttClient client);

    /// <summary>
    /// 从池中移除连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <returns>如果成功移除则返回 true，否则返回 false</returns>
    bool RemoveConnection(string clientId);

    /// <summary>
    /// 清理池中的无效连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    Task CleanupAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取连接池统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    MqttConnectionPoolStatistics GetStatistics();
}

/// <summary>
/// MQTT 连接池实现
/// </summary>
public class MqttConnectionPool : IMqttConnectionPool
{
    private readonly ILogger<MqttConnectionPool> _logger;
    private readonly MqttConnectionPoolOptions _options;
    private readonly ConcurrentDictionary<string, PooledConnection> _connections = new();
    private readonly object _lockObject = new();
    private readonly MqttConnectionPoolStatistics _statistics = new();
    private bool _disposed;

    /// <summary>
    /// 初始化连接池
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="options">连接池选项</param>
    public MqttConnectionPool(
        ILogger<MqttConnectionPool> logger,
        IOptions<MqttConnectionPoolOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));

        _statistics.MaxPoolSize = _options.MaxPoolSize;
    }

    /// <summary>
    /// 当前池中的连接数
    /// </summary>
    public int PoolSize => _connections.Count;

    /// <summary>
    /// 最大池大小
    /// </summary>
    public int MaxPoolSize => _options.MaxPoolSize;

    /// <summary>
    /// 活跃连接数
    /// </summary>
    public int ActiveConnections => _connections.Values.Count(c => c.IsActive);

    /// <summary>
    /// 从池中获取连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <returns>连接对象，如果池中没有可用连接则返回 null</returns>
    public IMqttClient? GetConnection(string clientId)
    {
        if (string.IsNullOrEmpty(clientId) || _disposed)
            return null;

        if (_connections.TryGetValue(clientId, out var pooledConnection))
        {
            lock (_lockObject)
            {
                if (!pooledConnection.IsActive && !pooledConnection.IsExpired(_options.ConnectionTimeoutMs))
                {
                    pooledConnection.MarkAsActive();
                    _statistics.ConnectionsReused++;
                    
                    _logger.LogDebug("Connection retrieved from pool: {ClientId}", clientId);
                    return pooledConnection.Client;
                }
                else if (pooledConnection.IsExpired(_options.ConnectionTimeoutMs))
                {
                    // 连接已过期，从池中移除
                    _connections.TryRemove(clientId, out _);
                    pooledConnection.Dispose();
                    _statistics.ExpiredConnections++;
                    
                    _logger.LogDebug("Expired connection removed from pool: {ClientId}", clientId);
                }
            }
        }

        return null;
    }

    /// <summary>
    /// 将连接返回到池中
    /// </summary>
    /// <param name="client">要返回的客户端连接</param>
    /// <returns>如果成功返回到池中则返回 true，否则返回 false</returns>
    public bool ReturnConnection(IMqttClient client)
    {
        if (client == null || _disposed)
            return false;

        if (_connections.TryGetValue(client.ClientId, out var pooledConnection))
        {
            lock (_lockObject)
            {
                pooledConnection.MarkAsInactive();
                _logger.LogTrace("Connection returned to pool: {ClientId}", client.ClientId);
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 添加连接到池中
    /// </summary>
    /// <param name="client">要添加的客户端连接</param>
    /// <returns>如果成功添加则返回 true，否则返回 false</returns>
    public bool AddConnection(IMqttClient client)
    {
        if (client == null || _disposed)
            return false;

        lock (_lockObject)
        {
            // 检查池大小限制
            if (PoolSize >= MaxPoolSize)
            {
                _logger.LogWarning("Connection pool is full, cannot add connection: {ClientId}", client.ClientId);
                _statistics.RejectedConnections++;
                return false;
            }

            var pooledConnection = new PooledConnection(client);
            if (_connections.TryAdd(client.ClientId, pooledConnection))
            {
                _statistics.TotalConnections++;
                _statistics.CurrentPoolSize = PoolSize;
                
                _logger.LogDebug("Connection added to pool: {ClientId}", client.ClientId);
                return true;
            }
            else
            {
                pooledConnection.Dispose();
                _logger.LogWarning("Failed to add connection to pool (already exists): {ClientId}", client.ClientId);
                return false;
            }
        }
    }

    /// <summary>
    /// 从池中移除连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <returns>如果成功移除则返回 true，否则返回 false</returns>
    public bool RemoveConnection(string clientId)
    {
        if (string.IsNullOrEmpty(clientId) || _disposed)
            return false;

        if (_connections.TryRemove(clientId, out var pooledConnection))
        {
            lock (_lockObject)
            {
                pooledConnection.Dispose();
                _statistics.CurrentPoolSize = PoolSize;
                
                _logger.LogDebug("Connection removed from pool: {ClientId}", clientId);
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 清理池中的无效连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    public async Task CleanupAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;

        var expiredConnections = new List<string>();

        foreach (var kvp in _connections)
        {
            try
            {
                if (kvp.Value.IsExpired(_options.ConnectionTimeoutMs) || 
                    kvp.Value.Client.State == MqttClientState.Disconnected ||
                    kvp.Value.Client.State == MqttClientState.Error)
                {
                    expiredConnections.Add(kvp.Key);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking connection expiration: {ClientId}", kvp.Key);
                expiredConnections.Add(kvp.Key);
            }
        }

        if (expiredConnections.Count > 0)
        {
            _logger.LogInformation("Cleaning up {Count} expired connections from pool", expiredConnections.Count);

            foreach (var clientId in expiredConnections)
            {
                try
                {
                    RemoveConnection(clientId);
                    _statistics.ExpiredConnections++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error removing expired connection: {ClientId}", clientId);
                }
            }

            _statistics.LastCleanupTime = DateTime.UtcNow;
            _statistics.CleanedUpConnections += expiredConnections.Count;
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取连接池统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public MqttConnectionPoolStatistics GetStatistics()
    {
        _statistics.CurrentPoolSize = PoolSize;
        _statistics.ActiveConnections = ActiveConnections;
        _statistics.PoolUtilization = MaxPoolSize > 0 ? (double)PoolSize / MaxPoolSize * 100 : 0;

        return _statistics;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            _logger.LogInformation("Disposing connection pool with {Count} connections", PoolSize);

            foreach (var kvp in _connections)
            {
                try
                {
                    kvp.Value.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error disposing pooled connection: {ClientId}", kvp.Key);
                }
            }

            _connections.Clear();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during connection pool disposal");
        }

        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// 池化连接包装器
/// </summary>
internal class PooledConnection : IDisposable
{
    public IMqttClient Client { get; }
    public DateTime CreatedAt { get; }
    public DateTime LastUsedAt { get; private set; }
    public bool IsActive { get; private set; }

    public PooledConnection(IMqttClient client)
    {
        Client = client ?? throw new ArgumentNullException(nameof(client));
        CreatedAt = DateTime.UtcNow;
        LastUsedAt = CreatedAt;
        IsActive = false;
    }

    public void MarkAsActive()
    {
        IsActive = true;
        LastUsedAt = DateTime.UtcNow;
    }

    public void MarkAsInactive()
    {
        IsActive = false;
        LastUsedAt = DateTime.UtcNow;
    }

    public bool IsExpired(int timeoutMs)
    {
        return DateTime.UtcNow - LastUsedAt > TimeSpan.FromMilliseconds(timeoutMs);
    }

    public void Dispose()
    {
        Client?.Dispose();
    }
}

/// <summary>
/// 连接池统计信息
/// </summary>
public class MqttConnectionPoolStatistics
{
    /// <summary>
    /// 当前池大小
    /// </summary>
    public int CurrentPoolSize { get; set; }

    /// <summary>
    /// 最大池大小
    /// </summary>
    public int MaxPoolSize { get; set; }

    /// <summary>
    /// 活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }

    /// <summary>
    /// 总连接数
    /// </summary>
    public long TotalConnections { get; set; }

    /// <summary>
    /// 被拒绝的连接数
    /// </summary>
    public long RejectedConnections { get; set; }

    /// <summary>
    /// 重用的连接数
    /// </summary>
    public long ConnectionsReused { get; set; }

    /// <summary>
    /// 过期的连接数
    /// </summary>
    public long ExpiredConnections { get; set; }

    /// <summary>
    /// 池使用率（百分比）
    /// </summary>
    public double PoolUtilization { get; set; }

    /// <summary>
    /// 最后清理时间
    /// </summary>
    public DateTime LastCleanupTime { get; set; }

    /// <summary>
    /// 清理的连接数
    /// </summary>
    public long CleanedUpConnections { get; set; }
}

/// <summary>
/// 连接池选项
/// </summary>
public class MqttConnectionPoolOptions
{
    /// <summary>
    /// 最大池大小
    /// </summary>
    public int MaxPoolSize { get; set; } = 1000;

    /// <summary>
    /// 连接超时时间（毫秒）
    /// </summary>
    public int ConnectionTimeoutMs { get; set; } = 300000; // 5 分钟

    /// <summary>
    /// 清理间隔（毫秒）
    /// </summary>
    public int CleanupIntervalMs { get; set; } = 60000; // 1 分钟

    /// <summary>
    /// 是否启用连接池
    /// </summary>
    public bool EnableConnectionPool { get; set; } = true;
}
