using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Network;

namespace MqttBroker.Core.Client;

/// <summary>
/// MQTT 客户端管理器接口
/// </summary>
public interface IMqttClientManager : IDisposable
{
    /// <summary>
    /// 当前连接的客户端数量
    /// </summary>
    int ConnectedClientCount { get; }

    /// <summary>
    /// 最大客户端连接数
    /// </summary>
    int MaxClientConnections { get; }

    /// <summary>
    /// 客户端连接事件
    /// </summary>
    event EventHandler<MqttClientConnectedEventArgs>? ClientConnected;

    /// <summary>
    /// 客户端断开连接事件
    /// </summary>
    event EventHandler<MqttClientDisconnectedEventArgs>? ClientDisconnected;

    /// <summary>
    /// 客户端认证事件
    /// </summary>
    event EventHandler<MqttClientAuthenticatedEventArgs>? ClientAuthenticated;

    /// <summary>
    /// 处理客户端连接请求
    /// </summary>
    /// <param name="connection">网络连接</param>
    /// <param name="connectPacket">CONNECT 数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    Task<MqttConnectResult> HandleConnectAsync(IClientConnection connection, MqttConnectPacket connectPacket, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理客户端断开连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <param name="reason">断开原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task HandleDisconnectAsync(string clientId, DisconnectionReason reason, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <returns>客户端连接，如果不存在则返回 null</returns>
    IMqttClient? GetClient(string clientId);

    /// <summary>
    /// 获取所有连接的客户端
    /// </summary>
    /// <returns>所有客户端连接</returns>
    IEnumerable<IMqttClient> GetAllClients();

    /// <summary>
    /// 检查客户端是否已连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <returns>如果已连接则返回 true，否则返回 false</returns>
    bool IsClientConnected(string clientId);

    /// <summary>
    /// 强制断开客户端连接
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <param name="reason">断开原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>断开任务</returns>
    Task DisconnectClientAsync(string clientId, DisconnectionReason reason = DisconnectionReason.ServerDisconnected, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理超时的客户端连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    Task CleanupTimeoutClientsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端管理器统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    MqttClientManagerStatistics GetStatistics();
}

/// <summary>
/// MQTT 连接结果
/// </summary>
public class MqttConnectResult
{
    /// <summary>
    /// 连接是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 连接返回码 (MQTT 3.1.1)
    /// </summary>
    public MqttConnectReturnCode ReturnCode { get; set; }

    /// <summary>
    /// 原因码 (MQTT 5.0)
    /// </summary>
    public MqttReasonCode ReasonCode { get; set; }

    /// <summary>
    /// 会话是否存在
    /// </summary>
    public bool SessionPresent { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建成功的连接结果
    /// </summary>
    /// <param name="sessionPresent">会话是否存在</param>
    /// <returns>连接结果</returns>
    public static MqttConnectResult Success(bool sessionPresent = false)
    {
        return new MqttConnectResult
        {
            IsSuccess = true,
            ReturnCode = MqttConnectReturnCode.ConnectionAccepted,
            ReasonCode = MqttReasonCode.Success,
            SessionPresent = sessionPresent
        };
    }

    /// <summary>
    /// 创建失败的连接结果
    /// </summary>
    /// <param name="returnCode">返回码</param>
    /// <param name="reasonCode">原因码</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>连接结果</returns>
    public static MqttConnectResult Failure(MqttConnectReturnCode returnCode, MqttReasonCode reasonCode, string? errorMessage = null)
    {
        return new MqttConnectResult
        {
            IsSuccess = false,
            ReturnCode = returnCode,
            ReasonCode = reasonCode,
            SessionPresent = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 客户端管理器统计信息
/// </summary>
public class MqttClientManagerStatistics
{
    /// <summary>
    /// 当前连接的客户端数量
    /// </summary>
    public int ConnectedClients { get; set; }

    /// <summary>
    /// 最大客户端连接数
    /// </summary>
    public int MaxConnections { get; set; }

    /// <summary>
    /// 总连接数
    /// </summary>
    public long TotalConnections { get; set; }

    /// <summary>
    /// 总断开连接数
    /// </summary>
    public long TotalDisconnections { get; set; }

    /// <summary>
    /// 被拒绝的连接数
    /// </summary>
    public long RejectedConnections { get; set; }

    /// <summary>
    /// 认证失败的连接数
    /// </summary>
    public long AuthenticationFailures { get; set; }

    /// <summary>
    /// 超时连接数
    /// </summary>
    public long TimeoutConnections { get; set; }

    /// <summary>
    /// 平均连接持续时间（毫秒）
    /// </summary>
    public double AverageConnectionDuration { get; set; }

    /// <summary>
    /// 每秒连接数
    /// </summary>
    public double ConnectionsPerSecond { get; set; }

    /// <summary>
    /// 连接使用率（百分比）
    /// </summary>
    public double ConnectionUtilization => MaxConnections > 0 ? (double)ConnectedClients / MaxConnections * 100 : 0;

    /// <summary>
    /// 按协议版本分组的连接数
    /// </summary>
    public Dictionary<MqttProtocolVersion, int> ConnectionsByProtocolVersion { get; set; } = new();

    /// <summary>
    /// 按 Clean Session 标志分组的连接数
    /// </summary>
    public Dictionary<bool, int> ConnectionsByCleanSession { get; set; } = new();

    /// <summary>
    /// 最后清理时间
    /// </summary>
    public DateTime LastCleanupTime { get; set; }

    /// <summary>
    /// 清理的连接数
    /// </summary>
    public long CleanedUpConnections { get; set; }
}
