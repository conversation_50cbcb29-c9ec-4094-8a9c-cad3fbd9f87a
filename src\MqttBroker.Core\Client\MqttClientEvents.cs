using MqttBroker.Core.Network;

namespace MqttBroker.Core.Client;

/// <summary>
/// MQTT 客户端连接事件参数
/// </summary>
public class MqttClientConnectedEventArgs : EventArgs
{
    /// <summary>
    /// MQTT 客户端
    /// </summary>
    public IMqttClient Client { get; }

    /// <summary>
    /// 初始化客户端连接事件参数
    /// </summary>
    /// <param name="client">MQTT 客户端</param>
    public MqttClientConnectedEventArgs(IMqttClient client)
    {
        Client = client ?? throw new ArgumentNullException(nameof(client));
    }
}

/// <summary>
/// MQTT 客户端断开连接事件参数
/// </summary>
public class MqttClientDisconnectedEventArgs : EventArgs
{
    /// <summary>
    /// MQTT 客户端
    /// </summary>
    public IMqttClient Client { get; }

    /// <summary>
    /// 断开连接原因
    /// </summary>
    public DisconnectionReason Reason { get; }

    /// <summary>
    /// 异常信息（如果有）
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// 初始化客户端断开连接事件参数
    /// </summary>
    /// <param name="client">MQTT 客户端</param>
    /// <param name="reason">断开连接原因</param>
    /// <param name="exception">异常信息</param>
    public MqttClientDisconnectedEventArgs(IMqttClient client, DisconnectionReason reason, Exception? exception = null)
    {
        Client = client ?? throw new ArgumentNullException(nameof(client));
        Reason = reason;
        Exception = exception;
    }
}

/// <summary>
/// MQTT 客户端认证事件参数
/// </summary>
public class MqttClientAuthenticatedEventArgs : EventArgs
{
    /// <summary>
    /// MQTT 客户端
    /// </summary>
    public IMqttClient Client { get; }

    /// <summary>
    /// 认证是否成功
    /// </summary>
    public bool IsSuccess { get; }

    /// <summary>
    /// 认证失败原因（如果有）
    /// </summary>
    public string? FailureReason { get; }

    /// <summary>
    /// 初始化客户端认证事件参数
    /// </summary>
    /// <param name="client">MQTT 客户端</param>
    /// <param name="isSuccess">认证是否成功</param>
    /// <param name="failureReason">认证失败原因</param>
    public MqttClientAuthenticatedEventArgs(IMqttClient client, bool isSuccess, string? failureReason = null)
    {
        Client = client ?? throw new ArgumentNullException(nameof(client));
        IsSuccess = isSuccess;
        FailureReason = failureReason;
    }
}
