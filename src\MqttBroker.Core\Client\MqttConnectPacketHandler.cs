using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Network;

namespace MqttBroker.Core.Client;

/// <summary>
/// MQTT CONNECT 数据包处理器
/// </summary>
public class MqttConnectPacketHandler : IPacketHandler
{
    private readonly ILogger<MqttConnectPacketHandler> _logger;
    private readonly IMqttClientManager _clientManager;

    /// <summary>
    /// 初始化 CONNECT 数据包处理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="clientManager">客户端管理器</param>
    public MqttConnectPacketHandler(
        ILogger<MqttConnectPacketHandler> logger,
        IMqttClientManager clientManager)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _clientManager = clientManager ?? throw new ArgumentNullException(nameof(clientManager));
    }

    /// <summary>
    /// 支持的数据包类型
    /// </summary>
    public MqttPacketType PacketType => MqttPacketType.Connect;

    /// <summary>
    /// 处理数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="packet">数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task HandleAsync(IClientConnection connection, IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        if (connection == null)
            throw new ArgumentNullException(nameof(connection));
        if (packet == null)
            throw new ArgumentNullException(nameof(packet));

        if (packet is not MqttConnectPacket connectPacket)
        {
            _logger.LogError("Invalid packet type for CONNECT handler: {PacketType}", packet.PacketType);
            await connection.CloseAsync(DisconnectionReason.ProtocolError, cancellationToken);
            return;
        }

        try
        {
            _logger.LogInformation("Processing CONNECT packet from {RemoteEndPoint}, ClientId: {ClientId}", 
                connection.RemoteEndPoint, connectPacket.ClientId);

            // 验证数据包有效性
            if (!connectPacket.IsValid())
            {
                _logger.LogWarning("Invalid CONNECT packet from {RemoteEndPoint}", connection.RemoteEndPoint);
                await SendConnAckAsync(connection, false, MqttConnectReturnCode.UnacceptableProtocolVersion, 
                    MqttReasonCode.ProtocolError, connectPacket.ProtocolVersion, cancellationToken);
                return;
            }

            // 处理连接请求
            var connectResult = await _clientManager.HandleConnectAsync(connection, connectPacket, cancellationToken);

            // 发送 CONNACK 响应
            await SendConnAckAsync(connection, connectResult.SessionPresent, connectResult.ReturnCode, 
                connectResult.ReasonCode, connectPacket.ProtocolVersion, cancellationToken);

            // 如果连接失败，关闭连接
            if (!connectResult.IsSuccess)
            {
                _logger.LogWarning("Connection rejected for {RemoteEndPoint}, ClientId: {ClientId}, Reason: {Reason}", 
                    connection.RemoteEndPoint, connectPacket.ClientId, connectResult.ErrorMessage);
                
                await Task.Delay(100, cancellationToken); // 给客户端一点时间接收 CONNACK
                await connection.CloseAsync(DisconnectionReason.AuthenticationFailed, cancellationToken);
            }
            else
            {
                _logger.LogInformation("Connection accepted for {RemoteEndPoint}, ClientId: {ClientId}", 
                    connection.RemoteEndPoint, connectPacket.ClientId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing CONNECT packet from {RemoteEndPoint}", connection.RemoteEndPoint);
            
            try
            {
                await SendConnAckAsync(connection, false, MqttConnectReturnCode.ServerUnavailable, 
                    MqttReasonCode.ServerUnavailable, connectPacket.ProtocolVersion, cancellationToken);
                await connection.CloseAsync(DisconnectionReason.ProtocolError, cancellationToken);
            }
            catch (Exception sendEx)
            {
                _logger.LogError(sendEx, "Error sending error CONNACK to {RemoteEndPoint}", connection.RemoteEndPoint);
            }
        }
    }

    /// <summary>
    /// 发送 CONNACK 数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="sessionPresent">会话是否存在</param>
    /// <param name="returnCode">返回码</param>
    /// <param name="reasonCode">原因码</param>
    /// <param name="protocolVersion">协议版本</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    private async Task SendConnAckAsync(
        IClientConnection connection,
        bool sessionPresent,
        MqttConnectReturnCode returnCode,
        MqttReasonCode reasonCode,
        MqttProtocolVersion protocolVersion,
        CancellationToken cancellationToken)
    {
        try
        {
            var connAckPacket = new MqttConnAckPacket
            {
                SessionPresent = sessionPresent,
                ReturnCode = returnCode,
                ReasonCode = reasonCode
            };

            // 对于 MQTT 5.0，可以添加属性
            if (protocolVersion == MqttProtocolVersion.Version50)
            {
                connAckPacket.Properties = new MqttProperties();
                
                // 可以添加服务器特定的属性
                // connAckPacket.Properties.Add(MqttPropertyType.ServerKeepAlive, 60);
                // connAckPacket.Properties.Add(MqttPropertyType.MaximumPacketSize, 65535);
            }

            await connection.SendPacketAsync(connAckPacket, cancellationToken);

            _logger.LogDebug("CONNACK sent to {RemoteEndPoint}, SessionPresent: {SessionPresent}, ReturnCode: {ReturnCode}", 
                connection.RemoteEndPoint, sessionPresent, returnCode);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending CONNACK to {RemoteEndPoint}", connection.RemoteEndPoint);
            throw;
        }
    }
}

/// <summary>
/// MQTT DISCONNECT 数据包处理器
/// </summary>
public class MqttDisconnectPacketHandler : IPacketHandler
{
    private readonly ILogger<MqttDisconnectPacketHandler> _logger;
    private readonly IMqttClientManager _clientManager;

    /// <summary>
    /// 初始化 DISCONNECT 数据包处理器
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="clientManager">客户端管理器</param>
    public MqttDisconnectPacketHandler(
        ILogger<MqttDisconnectPacketHandler> logger,
        IMqttClientManager clientManager)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _clientManager = clientManager ?? throw new ArgumentNullException(nameof(clientManager));
    }

    /// <summary>
    /// 支持的数据包类型
    /// </summary>
    public MqttPacketType PacketType => MqttPacketType.Disconnect;

    /// <summary>
    /// 处理数据包
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="packet">数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task HandleAsync(IClientConnection connection, IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        if (connection == null)
            throw new ArgumentNullException(nameof(connection));
        if (packet == null)
            throw new ArgumentNullException(nameof(packet));

        if (packet is not MqttDisconnectPacket disconnectPacket)
        {
            _logger.LogError("Invalid packet type for DISCONNECT handler: {PacketType}", packet.PacketType);
            return;
        }

        try
        {
            var clientId = connection.ClientId;
            if (string.IsNullOrEmpty(clientId))
            {
                _logger.LogWarning("Received DISCONNECT from connection without client ID: {ConnectionId}", connection.Id);
                await connection.CloseAsync(DisconnectionReason.ClientDisconnected, cancellationToken);
                return;
            }

            _logger.LogInformation("Processing DISCONNECT packet from client: {ClientId}", clientId);

            // 处理客户端断开连接
            await _clientManager.HandleDisconnectAsync(clientId, DisconnectionReason.ClientDisconnected, cancellationToken);

            _logger.LogInformation("Client disconnected gracefully: {ClientId}", clientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing DISCONNECT packet from {RemoteEndPoint}", connection.RemoteEndPoint);
            
            try
            {
                await connection.CloseAsync(DisconnectionReason.ProtocolError, cancellationToken);
            }
            catch (Exception closeEx)
            {
                _logger.LogError(closeEx, "Error closing connection after DISCONNECT error: {ConnectionId}", connection.Id);
            }
        }
    }
}
