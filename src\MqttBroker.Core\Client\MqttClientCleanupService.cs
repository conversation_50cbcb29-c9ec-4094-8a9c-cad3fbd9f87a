using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace MqttBroker.Core.Client;

/// <summary>
/// MQTT 客户端清理后台服务
/// </summary>
public class MqttClientCleanupService : BackgroundService
{
    private readonly ILogger<MqttClientCleanupService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly MqttClientCleanupOptions _options;

    /// <summary>
    /// 初始化客户端清理服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="options">清理选项</param>
    public MqttClientCleanupService(
        ILogger<MqttClientCleanupService> logger,
        IServiceProvider serviceProvider,
        IOptions<MqttClientCleanupOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
    }

    /// <summary>
    /// 执行后台任务
    /// </summary>
    /// <param name="stoppingToken">停止令牌</param>
    /// <returns>执行任务</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("MQTT Client Cleanup Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformCleanupAsync(stoppingToken);
                await Task.Delay(_options.CleanupIntervalMs, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // 正常取消，退出循环
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during client cleanup");
                
                // 发生错误时等待一段时间再重试
                try
                {
                    await Task.Delay(_options.ErrorRetryDelayMs, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }
        }

        _logger.LogInformation("MQTT Client Cleanup Service stopped");
    }

    /// <summary>
    /// 执行清理操作
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    private async Task PerformCleanupAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        
        try
        {
            // 清理超时的客户端连接
            if (_options.EnableClientTimeout)
            {
                var clientManager = scope.ServiceProvider.GetService<IMqttClientManager>();
                if (clientManager != null)
                {
                    await clientManager.CleanupTimeoutClientsAsync(cancellationToken);
                }
            }

            // 清理连接池
            if (_options.EnableConnectionPoolCleanup)
            {
                var connectionPool = scope.ServiceProvider.GetService<IMqttConnectionPool>();
                if (connectionPool != null)
                {
                    await connectionPool.CleanupAsync(cancellationToken);
                }
            }

            // 记录统计信息
            if (_options.LogStatistics)
            {
                await LogStatisticsAsync(scope.ServiceProvider, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cleanup operations");
            throw;
        }
    }

    /// <summary>
    /// 记录统计信息
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>记录任务</returns>
    private async Task LogStatisticsAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        try
        {
            var clientManager = serviceProvider.GetService<IMqttClientManager>();
            if (clientManager != null)
            {
                var stats = clientManager.GetStatistics();
                _logger.LogInformation(
                    "Client Manager Statistics: Connected={ConnectedClients}, Total={TotalConnections}, " +
                    "Rejected={RejectedConnections}, AuthFailures={AuthenticationFailures}, " +
                    "Timeouts={TimeoutConnections}, Utilization={ConnectionUtilization:F2}%",
                    stats.ConnectedClients, stats.TotalConnections, stats.RejectedConnections,
                    stats.AuthenticationFailures, stats.TimeoutConnections, stats.ConnectionUtilization);
            }

            var connectionPool = serviceProvider.GetService<IMqttConnectionPool>();
            if (connectionPool != null)
            {
                var poolStats = connectionPool.GetStatistics();
                _logger.LogInformation(
                    "Connection Pool Statistics: PoolSize={CurrentPoolSize}, Active={ActiveConnections}, " +
                    "Total={TotalConnections}, Reused={ConnectionsReused}, Expired={ExpiredConnections}, " +
                    "Utilization={PoolUtilization:F2}%",
                    poolStats.CurrentPoolSize, poolStats.ActiveConnections, poolStats.TotalConnections,
                    poolStats.ConnectionsReused, poolStats.ExpiredConnections, poolStats.PoolUtilization);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging statistics");
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping MQTT Client Cleanup Service...");
        await base.StopAsync(cancellationToken);
        _logger.LogInformation("MQTT Client Cleanup Service stopped");
    }
}

/// <summary>
/// MQTT 客户端清理选项
/// </summary>
public class MqttClientCleanupOptions
{
    /// <summary>
    /// 清理间隔（毫秒）
    /// </summary>
    public int CleanupIntervalMs { get; set; } = 30000; // 30 秒

    /// <summary>
    /// 错误重试延迟（毫秒）
    /// </summary>
    public int ErrorRetryDelayMs { get; set; } = 5000; // 5 秒

    /// <summary>
    /// 是否启用客户端超时清理
    /// </summary>
    public bool EnableClientTimeout { get; set; } = true;

    /// <summary>
    /// 是否启用连接池清理
    /// </summary>
    public bool EnableConnectionPoolCleanup { get; set; } = true;

    /// <summary>
    /// 是否记录统计信息
    /// </summary>
    public bool LogStatistics { get; set; } = true;

    /// <summary>
    /// 统计信息记录间隔（毫秒）
    /// </summary>
    public int StatisticsLogIntervalMs { get; set; } = 300000; // 5 分钟
}
