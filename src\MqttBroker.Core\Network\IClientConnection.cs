using System.Buffers;
using System.Net;
using MqttBroker.Core.Protocol;

namespace MqttBroker.Core.Network;

/// <summary>
/// 客户端连接接口
/// </summary>
public interface IClientConnection : IDisposable
{
    /// <summary>
    /// 连接唯一标识符
    /// </summary>
    string Id { get; }

    /// <summary>
    /// 客户端 ID（MQTT 协议层面）
    /// </summary>
    string? ClientId { get; set; }

    /// <summary>
    /// 远程端点
    /// </summary>
    EndPoint RemoteEndPoint { get; }

    /// <summary>
    /// 本地端点
    /// </summary>
    EndPoint LocalEndPoint { get; }

    /// <summary>
    /// 连接状态
    /// </summary>
    ConnectionState State { get; }

    /// <summary>
    /// 连接建立时间
    /// </summary>
    DateTime ConnectedAt { get; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    DateTime LastActivity { get; }

    /// <summary>
    /// MQTT 协议版本
    /// </summary>
    MqttProtocolVersion? ProtocolVersion { get; set; }

    /// <summary>
    /// 是否已认证
    /// </summary>
    bool IsAuthenticated { get; set; }

    /// <summary>
    /// 保活间隔（秒）
    /// </summary>
    int KeepAliveInterval { get; set; }

    /// <summary>
    /// 连接属性（用于存储自定义数据）
    /// </summary>
    IDictionary<string, object> Properties { get; }

    /// <summary>
    /// 数据包接收事件
    /// </summary>
    event EventHandler<PacketReceivedEventArgs>? PacketReceived;

    /// <summary>
    /// 连接关闭事件
    /// </summary>
    event EventHandler<ConnectionClosedEventArgs>? ConnectionClosed;

    /// <summary>
    /// 发送数据包
    /// </summary>
    /// <param name="packet">要发送的数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task SendPacketAsync(IMqttPacket packet, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送原始数据
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task SendDataAsync(ReadOnlyMemory<byte> data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 关闭连接
    /// </summary>
    /// <param name="reason">关闭原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>关闭任务</returns>
    Task CloseAsync(DisconnectionReason reason = DisconnectionReason.ServerDisconnected, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新最后活动时间
    /// </summary>
    void UpdateLastActivity();

    /// <summary>
    /// 获取连接统计信息
    /// </summary>
    /// <returns>连接统计信息</returns>
    ClientConnectionStatistics GetStatistics();
}

/// <summary>
/// 连接状态
/// </summary>
public enum ConnectionState
{
    /// <summary>
    /// 正在连接
    /// </summary>
    Connecting,

    /// <summary>
    /// 已连接
    /// </summary>
    Connected,

    /// <summary>
    /// 正在断开
    /// </summary>
    Disconnecting,

    /// <summary>
    /// 已断开
    /// </summary>
    Disconnected,

    /// <summary>
    /// 错误状态
    /// </summary>
    Error
}

/// <summary>
/// 断开连接原因
/// </summary>
public enum DisconnectionReason
{
    /// <summary>
    /// 客户端主动断开
    /// </summary>
    ClientDisconnected,

    /// <summary>
    /// 服务器主动断开
    /// </summary>
    ServerDisconnected,

    /// <summary>
    /// 网络错误
    /// </summary>
    NetworkError,

    /// <summary>
    /// 协议错误
    /// </summary>
    ProtocolError,

    /// <summary>
    /// 超时
    /// </summary>
    Timeout,

    /// <summary>
    /// 服务器关闭
    /// </summary>
    ServerShutdown,

    /// <summary>
    /// 连接限制
    /// </summary>
    ConnectionLimit,

    /// <summary>
    /// 认证失败
    /// </summary>
    AuthenticationFailed
}

/// <summary>
/// 数据包接收事件参数
/// </summary>
public class PacketReceivedEventArgs : EventArgs
{
    /// <summary>
    /// 接收到的数据包
    /// </summary>
    public IMqttPacket Packet { get; }

    /// <summary>
    /// 初始化数据包接收事件参数
    /// </summary>
    /// <param name="packet">接收到的数据包</param>
    public PacketReceivedEventArgs(IMqttPacket packet)
    {
        Packet = packet ?? throw new ArgumentNullException(nameof(packet));
    }
}

/// <summary>
/// 连接关闭事件参数
/// </summary>
public class ConnectionClosedEventArgs : EventArgs
{
    /// <summary>
    /// 关闭原因
    /// </summary>
    public DisconnectionReason Reason { get; }

    /// <summary>
    /// 异常信息（如果有）
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// 初始化连接关闭事件参数
    /// </summary>
    /// <param name="reason">关闭原因</param>
    /// <param name="exception">异常信息</param>
    public ConnectionClosedEventArgs(DisconnectionReason reason, Exception? exception = null)
    {
        Reason = reason;
        Exception = exception;
    }
}

/// <summary>
/// 客户端连接统计信息
/// </summary>
public class ClientConnectionStatistics
{
    /// <summary>
    /// 连接 ID
    /// </summary>
    public string ConnectionId { get; set; } = string.Empty;

    /// <summary>
    /// 客户端 ID
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// 远程端点
    /// </summary>
    public string RemoteEndPoint { get; set; } = string.Empty;

    /// <summary>
    /// 连接建立时间
    /// </summary>
    public DateTime ConnectedAt { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivity { get; set; }

    /// <summary>
    /// 连接持续时间
    /// </summary>
    public TimeSpan Duration => DateTime.UtcNow - ConnectedAt;

    /// <summary>
    /// 接收的字节数
    /// </summary>
    public long BytesReceived { get; set; }

    /// <summary>
    /// 发送的字节数
    /// </summary>
    public long BytesSent { get; set; }

    /// <summary>
    /// 接收的数据包数
    /// </summary>
    public long PacketsReceived { get; set; }

    /// <summary>
    /// 发送的数据包数
    /// </summary>
    public long PacketsSent { get; set; }

    /// <summary>
    /// 错误数
    /// </summary>
    public long Errors { get; set; }

    /// <summary>
    /// MQTT 协议版本
    /// </summary>
    public MqttProtocolVersion? ProtocolVersion { get; set; }

    /// <summary>
    /// 是否已认证
    /// </summary>
    public bool IsAuthenticated { get; set; }

    /// <summary>
    /// 保活间隔（秒）
    /// </summary>
    public int KeepAliveInterval { get; set; }
}
