{"MqttClientManagerOptions": {"MaxConnections": 10000, "AllowClientIdReuse": true, "TimeoutCheckIntervalMs": 30000, "EnableStatistics": true}, "MqttClientAuthenticationOptions": {"RequireAuthentication": true, "AllowEmptyClientId": false, "MaxClientIdLength": 23, "UseStrictClientIdValidation": true, "StaticUsers": {"admin": "admin123", "test": "test123", "guest": "guest123", "device001": "device001_secret", "sensor_hub": "sensor_hub_password"}, "AuthenticationTimeoutMs": 5000}, "MqttConnectionPoolOptions": {"MaxPoolSize": 1000, "ConnectionTimeoutMs": 300000, "CleanupIntervalMs": 60000, "EnableConnectionPool": true}, "MqttClientCleanupOptions": {"CleanupIntervalMs": 30000, "ErrorRetryDelayMs": 5000, "EnableClientTimeout": true, "EnableConnectionPoolCleanup": true, "LogStatistics": true, "StatisticsLogIntervalMs": 300000}, "Logging": {"LogLevel": {"Default": "Information", "MqttBroker.Core.Client": "Debug", "MqttBroker.Core.Client.MqttClientManager": "Information", "MqttBroker.Core.Client.MqttConnectionPool": "Information", "MqttBroker.Core.Client.MqttClientCleanupService": "Information"}}}