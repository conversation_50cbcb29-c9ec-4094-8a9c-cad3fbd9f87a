using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MqttBroker.Core;
using MqttBroker.Network.Abstractions;

namespace MqttBroker.Network.Examples;

/// <summary>
/// 简单的网络层演示程序
/// </summary>
public class SimpleNetworkDemo
{
    /// <summary>
    /// 运行演示程序
    /// </summary>
    /// <returns>运行任务</returns>
    public static async Task RunAsync()
    {
        Console.WriteLine("=== MQTT Broker 网络层演示程序 ===");
        Console.WriteLine();

        // 创建配置
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["MqttBroker:Network:Tcp:Enabled"] = "true",
                ["MqttBroker:Network:Tcp:Port"] = "1883",
                ["MqttBroker:Network:Tcp:Address"] = "127.0.0.1",
                ["MqttBroker:Network:Connection:MaxConnections"] = "1000",
                ["MqttBroker:Network:Connection:ConnectionTimeout"] = "30",
                ["MqttBroker:Network:Connection:KeepAliveTimeout"] = "60"
            })
            .Build();

        // 创建服务容器
        var services = new ServiceCollection();
        
        // 添加日志
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        // 注册核心服务
        services.AddMqttBrokerCore();
        
        // 注册网络服务
        services.AddMqttBrokerNetwork(configuration);

        // 构建服务提供者
        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<SimpleNetworkDemo>>();

        try
        {
            // 获取网络服务器
            var networkServer = serviceProvider.GetRequiredService<INetworkServer>();
            var connectionManager = serviceProvider.GetRequiredService<IConnectionManager>();

            // 订阅连接事件
            networkServer.ClientConnected += (sender, e) =>
            {
                logger.LogInformation("客户端已连接: {ConnectionId} from {RemoteEndPoint}", 
                    e.Connection.Id, e.Connection.RemoteEndPoint);
            };

            networkServer.ClientDisconnected += (sender, e) =>
            {
                logger.LogInformation("客户端已断开: {ConnectionId}, 原因: {Reason}", 
                    e.Connection.Id, e.Reason);
            };

            // 启动网络服务器
            logger.LogInformation("正在启动 MQTT Broker 网络服务器...");
            await networkServer.StartAsync();

            logger.LogInformation("网络服务器已启动，监听地址: {LocalEndPoint}", networkServer.LocalEndPoint);
            logger.LogInformation("最大连接数: {MaxConnections}", connectionManager.MaxConnections);
            logger.LogInformation("");
            logger.LogInformation("您可以使用 MQTT 客户端工具连接到 tcp://127.0.0.1:1883 进行测试");
            logger.LogInformation("按 'q' 键退出程序...");
            logger.LogInformation("");

            // 启动统计信息输出任务
            var statsTask = Task.Run(async () =>
            {
                while (true)
                {
                    await Task.Delay(10000); // 每10秒输出一次统计信息
                    
                    var serverStats = networkServer.GetStatistics();
                    var connectionStats = connectionManager.GetStatistics();

                    logger.LogInformation("=== 服务器统计信息 ===");
                    logger.LogInformation("当前连接数: {CurrentConnections}", serverStats.CurrentConnections);
                    logger.LogInformation("总连接数: {TotalConnections}", serverStats.TotalConnections);
                    logger.LogInformation("总断开数: {TotalDisconnections}", serverStats.TotalDisconnections);
                    logger.LogInformation("接收字节数: {BytesReceived:N0}", serverStats.BytesReceived);
                    logger.LogInformation("发送字节数: {BytesSent:N0}", serverStats.BytesSent);
                    logger.LogInformation("错误数: {Errors}", serverStats.Errors);
                    logger.LogInformation("连接使用率: {Utilization:F2}%", connectionStats.ConnectionUtilization);
                    logger.LogInformation("");
                }
            });

            // 等待用户输入
            while (true)
            {
                var key = Console.ReadKey(true);
                if (key.KeyChar == 'q' || key.KeyChar == 'Q')
                {
                    break;
                }
            }

            // 停止服务器
            logger.LogInformation("正在停止网络服务器...");
            await networkServer.StopAsync();
            logger.LogInformation("网络服务器已停止");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "演示程序运行出错");
        }
        finally
        {
            serviceProvider.Dispose();
        }

        Console.WriteLine("演示程序已结束");
    }
}

/// <summary>
/// 演示程序入口点
/// </summary>
public class Program
{
    /// <summary>
    /// 主入口点
    /// </summary>
    /// <param name="args">命令行参数</param>
    /// <returns>运行任务</returns>
    public static async Task Main(string[] args)
    {
        await SimpleNetworkDemo.RunAsync();
    }
}
