using Microsoft.Extensions.Logging;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;
using MqttBroker.Core.Network;
using System.Collections.Concurrent;

namespace MqttBroker.Core.Client;

/// <summary>
/// MQTT 客户端实现
/// </summary>
public class MqttClient : IMqttClient
{
    private readonly ILogger<MqttClient> _logger;
    private readonly object _lockObject = new();
    private readonly ConcurrentDictionary<string, object> _properties = new();
    private MqttClientState _state = MqttClientState.Connecting;
    private DateTime _lastActivity = DateTime.UtcNow;
    private bool _disposed;

    /// <summary>
    /// 初始化 MQTT 客户端
    /// </summary>
    /// <param name="clientId">客户端 ID</param>
    /// <param name="connection">网络连接</param>
    /// <param name="connectPacket">CONNECT 数据包</param>
    /// <param name="logger">日志记录器</param>
    public MqttClient(
        string clientId,
        IClientConnection connection,
        MqttConnectPacket connectPacket,
        ILogger<MqttClient> logger)
    {
        ClientId = clientId ?? throw new ArgumentNullException(nameof(clientId));
        Connection = connection ?? throw new ArgumentNullException(nameof(connection));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        if (connectPacket == null)
            throw new ArgumentNullException(nameof(connectPacket));

        // 从 CONNECT 数据包中提取信息
        ProtocolVersion = connectPacket.ProtocolVersion;
        CleanSession = connectPacket.CleanSession;
        KeepAliveInterval = connectPacket.KeepAlive;
        Username = connectPacket.Username;
        WillMessage = connectPacket.WillMessage;

        ConnectedAt = DateTime.UtcNow;
        _lastActivity = ConnectedAt;

        // 订阅网络连接事件
        Connection.PacketReceived += OnPacketReceived;
        Connection.ConnectionClosed += OnConnectionClosed;

        _logger.LogDebug("MQTT client created: {ClientId}", ClientId);
    }

    /// <summary>
    /// 客户端 ID
    /// </summary>
    public string ClientId { get; }

    /// <summary>
    /// 网络连接
    /// </summary>
    public IClientConnection Connection { get; }

    /// <summary>
    /// MQTT 协议版本
    /// </summary>
    public MqttProtocolVersion ProtocolVersion { get; }

    /// <summary>
    /// 是否已认证
    /// </summary>
    public bool IsAuthenticated { get; private set; }

    /// <summary>
    /// 是否为 Clean Session
    /// </summary>
    public bool CleanSession { get; }

    /// <summary>
    /// 保活间隔（秒）
    /// </summary>
    public ushort KeepAliveInterval { get; }

    /// <summary>
    /// 连接建立时间
    /// </summary>
    public DateTime ConnectedAt { get; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivity
    {
        get
        {
            lock (_lockObject)
            {
                return _lastActivity;
            }
        }
    }

    /// <summary>
    /// 用户名
    /// </summary>
    public string? Username { get; }

    /// <summary>
    /// 遗嘱消息
    /// </summary>
    public MqttWillMessage? WillMessage { get; }

    /// <summary>
    /// 客户端状态
    /// </summary>
    public MqttClientState State
    {
        get
        {
            lock (_lockObject)
            {
                return _state;
            }
        }
        private set
        {
            lock (_lockObject)
            {
                if (_state != value)
                {
                    var oldState = _state;
                    _state = value;
                    StateChanged?.Invoke(this, new MqttClientStateChangedEventArgs(oldState, value));
                    _logger.LogDebug("Client state changed: {ClientId}, {OldState} -> {NewState}", ClientId, oldState, value);
                }
            }
        }
    }

    /// <summary>
    /// 客户端属性（用于存储自定义数据）
    /// </summary>
    public IDictionary<string, object> Properties => _properties;

    /// <summary>
    /// 数据包接收事件
    /// </summary>
    public event EventHandler<MqttPacketReceivedEventArgs>? PacketReceived;

    /// <summary>
    /// 客户端状态变更事件
    /// </summary>
    public event EventHandler<MqttClientStateChangedEventArgs>? StateChanged;

    /// <summary>
    /// 发送 MQTT 数据包
    /// </summary>
    /// <param name="packet">要发送的数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    public async Task SendPacketAsync(IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        if (packet == null)
            throw new ArgumentNullException(nameof(packet));

        if (_disposed)
            throw new ObjectDisposedException(nameof(MqttClient));

        try
        {
            await Connection.SendPacketAsync(packet, cancellationToken);
            UpdateLastActivity();
            
            _logger.LogTrace("Packet sent to client: {ClientId}, PacketType: {PacketType}", ClientId, packet.PacketType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending packet to client: {ClientId}, PacketType: {PacketType}", ClientId, packet.PacketType);
            throw;
        }
    }

    /// <summary>
    /// 处理接收到的 MQTT 数据包
    /// </summary>
    /// <param name="packet">接收到的数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public async Task HandlePacketAsync(IMqttPacket packet, CancellationToken cancellationToken = default)
    {
        if (packet == null)
            throw new ArgumentNullException(nameof(packet));

        if (_disposed)
            return;

        try
        {
            UpdateLastActivity();

            _logger.LogTrace("Handling packet from client: {ClientId}, PacketType: {PacketType}", ClientId, packet.PacketType);

            // 触发数据包接收事件
            PacketReceived?.Invoke(this, new MqttPacketReceivedEventArgs(packet));

            // 根据数据包类型进行基本处理
            switch (packet.PacketType)
            {
                case MqttPacketType.PingReq:
                    await HandlePingRequestAsync(cancellationToken);
                    break;

                case MqttPacketType.Disconnect:
                    await HandleDisconnectAsync(packet as MqttDisconnectPacket, cancellationToken);
                    break;

                default:
                    // 其他数据包类型由上层处理器处理
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling packet from client: {ClientId}, PacketType: {PacketType}", ClientId, packet.PacketType);
            throw;
        }
    }

    /// <summary>
    /// 断开客户端连接
    /// </summary>
    /// <param name="reason">断开原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>断开任务</returns>
    public async Task DisconnectAsync(DisconnectionReason reason = DisconnectionReason.ServerDisconnected, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;

        try
        {
            State = MqttClientState.Disconnecting;

            _logger.LogInformation("Disconnecting client: {ClientId}, Reason: {Reason}", ClientId, reason);

            await Connection.CloseAsync(reason, cancellationToken);

            State = MqttClientState.Disconnected;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disconnecting client: {ClientId}", ClientId);
            State = MqttClientState.Error;
            throw;
        }
    }

    /// <summary>
    /// 更新最后活动时间
    /// </summary>
    public void UpdateLastActivity()
    {
        lock (_lockObject)
        {
            _lastActivity = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 检查客户端是否超时
    /// </summary>
    /// <returns>如果超时则返回 true，否则返回 false</returns>
    public bool IsTimeout()
    {
        if (KeepAliveInterval == 0)
            return false; // 没有设置保活间隔，不会超时

        var timeout = TimeSpan.FromSeconds(KeepAliveInterval * 1.5); // MQTT 标准建议的超时时间
        return DateTime.UtcNow - LastActivity > timeout;
    }

    /// <summary>
    /// 获取客户端统计信息
    /// </summary>
    /// <returns>客户端统计信息</returns>
    public MqttClientStatistics GetStatistics()
    {
        var connectionStats = Connection.GetStatistics();

        return new MqttClientStatistics
        {
            ClientId = ClientId,
            ConnectionId = Connection.Id,
            RemoteEndPoint = Connection.RemoteEndPoint.ToString(),
            ConnectedAt = ConnectedAt,
            LastActivity = LastActivity,
            BytesReceived = connectionStats.BytesReceived,
            BytesSent = connectionStats.BytesSent,
            PacketsReceived = connectionStats.PacketsReceived,
            PacketsSent = connectionStats.PacketsSent,
            Errors = connectionStats.Errors,
            ProtocolVersion = ProtocolVersion,
            IsAuthenticated = IsAuthenticated,
            CleanSession = CleanSession,
            KeepAliveInterval = KeepAliveInterval,
            Username = Username,
            State = State
        };
    }

    /// <summary>
    /// 设置认证状态
    /// </summary>
    /// <param name="isAuthenticated">是否已认证</param>
    internal void SetAuthenticated(bool isAuthenticated)
    {
        IsAuthenticated = isAuthenticated;
        if (isAuthenticated)
        {
            State = MqttClientState.Authenticated;
        }
    }

    /// <summary>
    /// 处理 PING 请求
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    private async Task HandlePingRequestAsync(CancellationToken cancellationToken)
    {
        var pingResp = new MqttPingPacket { PacketType = MqttPacketType.PingResp };
        await SendPacketAsync(pingResp, cancellationToken);
        
        _logger.LogTrace("PING response sent to client: {ClientId}", ClientId);
    }

    /// <summary>
    /// 处理断开连接请求
    /// </summary>
    /// <param name="disconnectPacket">断开连接数据包</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    private async Task HandleDisconnectAsync(MqttDisconnectPacket? disconnectPacket, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Client requested disconnect: {ClientId}", ClientId);
        await DisconnectAsync(DisconnectionReason.ClientDisconnected, cancellationToken);
    }

    /// <summary>
    /// 处理网络连接数据包接收事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private async void OnPacketReceived(object? sender, PacketReceivedEventArgs e)
    {
        try
        {
            await HandlePacketAsync(e.Packet);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling received packet for client: {ClientId}", ClientId);
        }
    }

    /// <summary>
    /// 处理网络连接关闭事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnConnectionClosed(object? sender, ConnectionClosedEventArgs e)
    {
        _logger.LogInformation("Network connection closed for client: {ClientId}, Reason: {Reason}", ClientId, e.Reason);
        State = MqttClientState.Disconnected;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            // 取消订阅事件
            if (Connection != null)
            {
                Connection.PacketReceived -= OnPacketReceived;
                Connection.ConnectionClosed -= OnConnectionClosed;
            }

            State = MqttClientState.Disconnected;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during MQTT client disposal: {ClientId}", ClientId);
        }

        GC.SuppressFinalize(this);
    }
}
