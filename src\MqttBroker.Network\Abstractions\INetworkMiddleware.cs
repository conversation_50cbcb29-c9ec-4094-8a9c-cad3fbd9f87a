using System.Collections.Concurrent;
using MqttBroker.Core.Protocol;

namespace MqttBroker.Network.Abstractions;

/// <summary>
/// 网络中间件接口
/// </summary>
public interface INetworkMiddleware
{
    /// <summary>
    /// 中间件名称
    /// </summary>
    string Name { get; }

    /// <summary>
    /// 中间件优先级（数值越小优先级越高）
    /// </summary>
    int Priority { get; }

    /// <summary>
    /// 处理连接建立
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="next">下一个中间件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task OnConnectionEstablishedAsync(IClientConnection connection, Func<Task> next, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理数据包接收
    /// </summary>
    /// <param name="packet">接收到的数据包</param>
    /// <param name="connection">客户端连接</param>
    /// <param name="next">下一个中间件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task OnPacketReceivedAsync(IMqttPacket packet, IClientConnection connection, Func<Task> next, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理数据包发送
    /// </summary>
    /// <param name="packet">要发送的数据包</param>
    /// <param name="connection">客户端连接</param>
    /// <param name="next">下一个中间件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task OnPacketSendingAsync(IMqttPacket packet, IClientConnection connection, Func<Task> next, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理连接关闭
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="reason">关闭原因</param>
    /// <param name="next">下一个中间件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task OnConnectionClosedAsync(IClientConnection connection, DisconnectionReason reason, Func<Task> next, CancellationToken cancellationToken = default);
}

/// <summary>
/// 网络中间件管理器接口
/// </summary>
public interface INetworkMiddlewareManager
{
    /// <summary>
    /// 注册中间件
    /// </summary>
    /// <param name="middleware">网络中间件</param>
    void RegisterMiddleware(INetworkMiddleware middleware);

    /// <summary>
    /// 注册中间件
    /// </summary>
    /// <typeparam name="T">中间件类型</typeparam>
    void RegisterMiddleware<T>() where T : class, INetworkMiddleware;

    /// <summary>
    /// 取消注册中间件
    /// </summary>
    /// <param name="middlewareType">中间件类型</param>
    void UnregisterMiddleware(Type middlewareType);

    /// <summary>
    /// 执行连接建立中间件链
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行任务</returns>
    Task ExecuteConnectionEstablishedAsync(IClientConnection connection, CancellationToken cancellationToken = default);

    /// <summary>
    /// 执行数据包接收中间件链
    /// </summary>
    /// <param name="packet">接收到的数据包</param>
    /// <param name="connection">客户端连接</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行任务</returns>
    Task ExecutePacketReceivedAsync(IMqttPacket packet, IClientConnection connection, CancellationToken cancellationToken = default);

    /// <summary>
    /// 执行数据包发送中间件链
    /// </summary>
    /// <param name="packet">要发送的数据包</param>
    /// <param name="connection">客户端连接</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行任务</returns>
    Task ExecutePacketSendingAsync(IMqttPacket packet, IClientConnection connection, CancellationToken cancellationToken = default);

    /// <summary>
    /// 执行连接关闭中间件链
    /// </summary>
    /// <param name="connection">客户端连接</param>
    /// <param name="reason">关闭原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行任务</returns>
    Task ExecuteConnectionClosedAsync(IClientConnection connection, DisconnectionReason reason, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取中间件统计信息
    /// </summary>
    /// <returns>中间件统计信息</returns>
    MiddlewareStatistics GetStatistics();
}

/// <summary>
/// 中间件统计信息
/// </summary>
public class MiddlewareStatistics
{
    /// <summary>
    /// 注册的中间件数量
    /// </summary>
    public int RegisteredMiddlewares { get; set; }

    /// <summary>
    /// 执行的中间件总数
    /// </summary>
    public long TotalExecutions { get; set; }

    /// <summary>
    /// 执行失败的中间件数
    /// </summary>
    public long FailedExecutions { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTime { get; set; }

    /// <summary>
    /// 每秒执行的中间件数
    /// </summary>
    public double ExecutionsPerSecond { get; set; }

    /// <summary>
    /// 按中间件类型分组的执行统计
    /// </summary>
    public ConcurrentDictionary<string, MiddlewareTypeStatistics> MiddlewareTypeStatistics { get; set; } = new();

    /// <summary>
    /// 最后执行时间
    /// </summary>
    public DateTime LastExecutionTime { get; set; }
}

/// <summary>
/// 中间件类型统计信息
/// </summary>
public class MiddlewareTypeStatistics
{
    /// <summary>
    /// 中间件名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 执行次数
    /// </summary>
    public long ExecutionCount { get; set; }

    /// <summary>
    /// 执行失败次数
    /// </summary>
    public long FailedCount { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTime { get; set; }

    /// <summary>
    /// 最小执行时间（毫秒）
    /// </summary>
    public double MinExecutionTime { get; set; }

    /// <summary>
    /// 最大执行时间（毫秒）
    /// </summary>
    public double MaxExecutionTime { get; set; }

    /// <summary>
    /// 最后执行时间
    /// </summary>
    public DateTime LastExecutionTime { get; set; }
}
